<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>评审管理 - 可研报告评审助手</title>
    <link href="/static/bootstrap.min.css" rel="stylesheet">
    <link href="/static/all.min.css" rel="stylesheet">
    <style>
        .review-card {
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .review-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
        }
        .review-content {
            padding: 15px;
        }
        .status-badge {
            font-size: 0.8em;
        }
        .stats-info {
            font-size: 0.9em;
            color: #6c757d;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">可研报告评审助手</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">汇总报告</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/topics">专题管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/reports">报告管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/reviews">评审管理</a>
                    </li>
                </ul>
                <!-- 全局专题过滤器 -->
                <div class="d-flex align-items-center">
                    <label class="text-white me-2" style="font-size: 0.9em;">专题:</label>
                    <select class="form-select form-select-sm" id="globalTopicFilter" onchange="onGlobalTopicChange()" style="width: 200px;">
                        <option value="">所有专题</option>
                    </select>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>评审管理</h1>
        </div>

        <!-- 过滤器 -->
        <div class="row mb-4">
            <div class="col-md-4">
                <label class="form-label">报告过滤</label>
                <select class="form-select" id="reportFilter" onchange="filterReports()">
                    <option value="">所有报告</option>
                </select>
            </div>
        </div>

        <!-- 加载动画 -->
        <div class="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">评审中...</span>
            </div>
            <p class="mt-2">正在评审报告，请稍候...</p>
        </div>

        <!-- 报告列表 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">报告列表</h5>
                <div>
                    <button type="button" class="btn btn-sm btn-primary" onclick="selectAllReports()">
                        <i class="fas fa-check-square"></i> 全选
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearAllReports()">
                        <i class="fas fa-square"></i> 清空
                    </button>
                    <button type="button" class="btn btn-sm btn-warning" onclick="startBatchReview()">
                        <i class="fas fa-tasks"></i> 批量评审
                    </button>
                    <button type="button" class="btn btn-sm btn-info" onclick="generateMultiReportSummary()">
                        <i class="fas fa-chart-line"></i> 汇总分析
                    </button>
                    <span id="selectedCount" class="ms-3 text-muted">已选择 0 个报告</span>
                </div>
            </div>
            <div class="card-body">
                <div id="reportsList">
                    <!-- 报告卡片将在这里动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 评审结果详情模态框 -->
    <div class="modal fade" id="reviewDetailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">评审结果详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="reviewDetailContent">
                        <!-- 评审详情内容将在这里显示 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let allReviews = [];
        let allReports = [];
        let allTopics = [];
        let selectedReports = new Set(); // 存储选中的报告ID

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTopics();
            loadReports();
            loadReviews();
        });

        // 加载专题列表
        async function loadTopics() {
            try {
                const response = await fetch('/api/topics');
                const result = await response.json();

                if (result.success) {
                    allTopics = result.data;
                    populateGlobalTopicFilter();
                }
            } catch (error) {
                console.error('加载专题列表失败:', error);
            }
        }

        // 填充全局专题过滤器
        function populateGlobalTopicFilter() {
            const select = document.getElementById('globalTopicFilter');
            select.innerHTML = '<option value="">所有专题</option>';

            allTopics.forEach(topic => {
                const option = document.createElement('option');
                option.value = topic.id;
                option.textContent = topic.name;
                select.appendChild(option);
            });
        }

        // 全局专题变化处理
        function onGlobalTopicChange() {
            const globalTopicId = document.getElementById('globalTopicFilter').value;
            filterReports();
        }

        // 加载报告列表
        async function loadReports() {
            try {
                const response = await fetch('/api/reports');
                const result = await response.json();

                if (result.success) {
                    allReports = result.data;
                    populateReportSelects();
                    displayReports(allReports);
                }
            } catch (error) {
                console.error('加载报告列表失败:', error);
            }
        }

        // 加载评审记录
        async function loadReviews() {
            try {
                const response = await fetch('/api/reviews');
                const result = await response.json();

                if (result.success) {
                    allReviews = result.data;
                    // displayReviews(allReviews);
                } else {
                    alert('加载评审记录失败: ' + result.error);
                }
            } catch (error) {
                alert('加载评审记录失败: ' + error.message);
            }
        }

        // 填充报告选择框
        function populateReportSelects() {
            const select = document.getElementById('reportFilter');
            select.innerHTML = '<option value="">所有报告</option>';

            allReports.forEach(report => {
                const option = document.createElement('option');
                option.value = report.id;
                const reviewStatus = report.review_status === 'reviewed' ? '已评审' : '未评审';
                option.textContent = `${report.name} [${reviewStatus}]`;
                select.appendChild(option);
            });
        }

        // 过滤报告
        function filterReports() {
            const reportId = document.getElementById('reportFilter').value;
            const topicId = document.getElementById('globalTopicFilter').value;
            let filteredReports = allReports;

            if (reportId) {
                filteredReports = filteredReports.filter(report => report.id === reportId);
            }

            if (topicId) {
                filteredReports = filteredReports.filter(report => report.topic_id === topicId);
            }

            displayReports(filteredReports);
        }

        // 显示报告列表
        function displayReports(reports) {
            const container = document.getElementById('reportsList');

            if (reports.length === 0) {
                container.innerHTML = '<div class="text-center text-muted"><p>暂无报告。</p></div>';
                return;
            }

            container.innerHTML = reports.map(report => {
                const topic = allTopics.find(t => t.id === report.topic_id);
                const topicName = topic ? topic.name : '未分类';

                // 使用API返回的评审状态信息
                const hasReview = report.review_status === 'reviewed';
                const review = hasReview ? allReviews.find(r => r.report_id === report.id) : null;

                // 获取统计信息
                const stats = review?.result?.statistics || {};
                const complianceRate = stats.compliance_rate || 0;
                const totalCriteria = stats.total_criteria || 0;

                const isSelected = selectedReports.has(report.id);

                return `
                    <div class="review-card">
                        <div class="review-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <input type="checkbox" class="form-check-input me-3"
                                           ${isSelected ? 'checked' : ''}
                                           onchange="toggleReportSelection('${report.id}')">
                                    <div>
                                        <h5 class="mb-1">${report.name}</h5>
                                        <span class="badge bg-secondary status-badge">${topicName}</span>
                                        ${hasReview ? '<span class="badge bg-success status-badge ms-2">已评审</span>' : '<span class="badge bg-warning status-badge ms-2">未评审</span>'}
                                    </div>
                                </div>
                                <div>
                                    ${hasReview ? `
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewReviewDetail('${review.id}')">
                                            <i class="fas fa-eye"></i> 查看详情
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-success" onclick="startReviewForReport('${report.id}')">
                                            <i class="fas fa-redo"></i> 重新评审
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteReview('${review.id}')">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                    ` : `
                                        <button type="button" class="btn btn-sm btn-success" onclick="startReviewForReport('${report.id}')">
                                            <i class="fas fa-play"></i> 开始评审
                                        </button>
                                    `}
                                </div>
                            </div>
                        </div>
                        ${hasReview ? `
                            <div class="review-content">
                                <div class="stats-info">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <strong>合规率:</strong> ${complianceRate}%
                                        </div>
                                        <div class="col-md-3">
                                            <strong>总审查细则:</strong> ${totalCriteria}
                                        </div>
                                        <div class="col-md-6">
                                            <strong>评审时间:</strong> ${new Date(review.created_at).toLocaleString()}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                `;
            }).join('');
        }

        // 开始评审（针对特定报告）
        async function startReviewForReport(reportId) {
            if (!reportId) {
                alert('报告ID无效');
                return;
            }

            // 检查是否已有评审记录
            const existingReview = allReviews.find(r => r.report_id === reportId);
            if (existingReview) {
                if (!confirm('该报告已有评审记录，确定要重新评审吗？')) {
                    return;
                }
            }

            document.querySelector('.loading').style.display = 'block';

            try {
                const response = await fetch(`/api/reviews/${reportId}`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    alert('评审完成');
                    loadReviews(); // 重新加载评审记录
                    displayReports(allReports); // 重新显示报告列表
                } else {
                    alert('评审失败: ' + result.error);
                }
            } catch (error) {
                alert('评审失败: ' + error.message);
            } finally {
                document.querySelector('.loading').style.display = 'none';
            }
        }

        // 查看评审详情
        async function viewReviewDetail(reviewId) {
            try {
                const response = await fetch(`/api/reviews/${reviewId}`);
                const result = await response.json();

                if (result.success) {
                    const review = result.data;
                    const report = allReports.find(r => r.id === review.report_id);
                    const topic = allTopics.find(t => t.id === review.topic_id);

                    displayReviewDetail(review, report, topic);
                    new bootstrap.Modal(document.getElementById('reviewDetailModal')).show();
                } else {
                    alert('获取评审详情失败: ' + result.error);
                }
            } catch (error) {
                alert('获取评审详情失败: ' + error.message);
            }
        }

        // 显示评审详情
        function displayReviewDetail(review, report, topic) {
            const container = document.getElementById('reviewDetailContent');
            const result = review.result;
            const stats = result.statistics || {};

            let content = `
                <div class="mb-4">
                    <h6>基本信息</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>报告名称:</strong> ${report ? report.name : '未知报告'}</p>
                            <p><strong>所属专题:</strong> ${topic ? topic.name : '未分类'}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>评审时间:</strong> ${new Date(review.created_at).toLocaleString()}</p>
                            <p><strong>评审状态:</strong> ${review.status}</p>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <h6>统计信息</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5 class="text-primary">${stats.total_criteria || 0}</h5>
                                <small>总审查细则</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5 class="text-success">${(stats.result_distribution?.['符合'] || 0) + (stats.result_distribution?.['基本符合'] || 0)}</h5>
                                <small>符合项</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5 class="text-danger">${stats.result_distribution?.['不符合'] || 0}</h5>
                                <small>不符合项</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5 class="text-info">${stats.compliance_rate || 0}%</h5>
                                <small>合规率</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 添加审查细则详情表格
            if (result.criteria_results && result.criteria_results.length > 0) {
                content += `
                    <div class="mb-4">
                        <h6>审查细则详情</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered table-sm">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 8%">编号</th>
                                        <th style="width: 15%">审查范畴</th>
                                        <th style="width: 40%">评审细则</th>
                                        <th style="width: 18%">审查情况</th>
                                        <th style="width: 19%">复核情况</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${generateCriteriaTable(result.criteria_results, report.id)}
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;
            }

            if (result.summary) {
                content += `
                    <div class="mb-4">
                        <h6>总体评审意见</h6>
                        <div class="p-3 bg-light rounded">
                            ${typeof result.summary === 'string' ? result.summary.replace(/\n/g, '<br>') : JSON.stringify(result.summary)}
                        </div>
                    </div>
                `;
            }

            container.innerHTML = content;
        }

        // 生成审查细则表格
        function generateCriteriaTable(criteriaResults, reportId) {
            // 获取该报告的所有评审记录，用于对比首次评审和复核情况
            const reportReviews = allReviews.filter(r => r.report_id === reportId);
            reportReviews.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

            let tableRows = '';

            // 处理不同的数据结构
            let processedCriteria = [];

            // 检查数据结构类型
            if (criteriaResults && criteriaResults.length > 0) {
                const firstItem = criteriaResults[0];

                // 如果是criteria_analysis格式（新格式）
                if (firstItem.section_results || firstItem.overall_result) {
                    processedCriteria = criteriaResults.map(criterion => ({
                        criterion_id: criterion.criterion_id,
                        criterion_content: criterion.criterion_content,
                        category: criterion.category || '',
                        overall_result: criterion.overall_result || '',
                        comprehensive_analysis: criterion.comprehensive_analysis || ''
                    }));
                }
                // 如果是review_results格式（旧格式）
                else if (firstItem.criterion_text || firstItem.result) {
                    processedCriteria = criteriaResults.map(criterion => ({
                        criterion_id: criterion.criterion_id,
                        criterion_content: criterion.criterion_text || criterion.criterion_content || '',
                        category: criterion.category || '',
                        overall_result: criterion.result || '',
                        comprehensive_analysis: criterion.analysis || criterion.explanation || ''
                    }));
                }
                // 如果是sections格式
                else if (firstItem.sections) {
                    processedCriteria = criteriaResults.map(criterion => ({
                        criterion_id: criterion.criterion_id,
                        criterion_content: criterion.criterion_content || '',
                        category: criterion.category || '',
                        overall_result: criterion.overall_result || '不适用',
                        comprehensive_analysis: criterion.comprehensive_analysis || ''
                    }));
                }
            }

            processedCriteria.forEach(criterion => {
                const criterionId = criterion.criterion_id;

                // 获取首次评审结果和最新评审结果
                let firstReviewResult = '';
                let latestReviewResult = '';

                if (reportReviews.length > 0) {
                    // 首次评审结果
                    const firstReview = reportReviews[0];
                    const firstCriterion = findCriterionInReview(firstReview, criterionId);
                    firstReviewResult = firstCriterion ? extractCriterionResult(firstCriterion) : '';

                    // 最新评审结果（如果有多次评审）
                    if (reportReviews.length > 1) {
                        const latestReview = reportReviews[reportReviews.length - 1];
                        const latestCriterion = findCriterionInReview(latestReview, criterionId);
                        latestReviewResult = latestCriterion ? extractCriterionResult(latestCriterion) : '';
                    } else {
                        latestReviewResult = firstReviewResult; // 只有一次评审时，复核情况与审查情况相同
                    }
                }

                // 如果没有找到历史记录，使用当前结果
                if (!firstReviewResult) {
                    firstReviewResult = criterion.overall_result + (criterion.comprehensive_analysis ? ': ' + criterion.comprehensive_analysis : '');
                }
                if (!latestReviewResult) {
                    latestReviewResult = criterion.overall_result + (criterion.comprehensive_analysis ? ': ' + criterion.comprehensive_analysis : '');
                }

                // 获取结果状态的CSS类
                const getStatusClass = (result) => {
                    if (result.includes('符合') && !result.includes('不符合')) {
                        return 'text-success';
                    } else if (result.includes('基本符合')) {
                        return 'text-warning';
                    } else if (result.includes('不符合')) {
                        return 'text-danger';
                    } else if (result.includes('不适用')) {
                        return 'text-muted';
                    }
                    return '';
                };

                tableRows += `
                    <tr>
                        <td>${criterion.criterion_id}</td>
                        <td>${criterion.category}</td>
                        <td class="text-wrap">${criterion.criterion_content}</td>
                        <td class="${getStatusClass(firstReviewResult)}">
                            ${firstReviewResult.length > 80 ? firstReviewResult.substring(0, 80) + '...' : firstReviewResult}
                        </td>
                        <td class="${getStatusClass(latestReviewResult)}">
                            ${latestReviewResult.length > 80 ? latestReviewResult.substring(0, 80) + '...' : latestReviewResult}
                        </td>
                    </tr>
                `;
            });

            return tableRows;
        }

        // 在评审记录中查找指定的审查细则
        function findCriterionInReview(review, criterionId) {
            const result = review.result;

            // 检查不同的数据结构
            if (result.criteria_analysis) {
                return result.criteria_analysis.find(c => c.criterion_id === criterionId);
            }
            if (result.criteria_results) {
                return result.criteria_results.find(c => c.criterion_id === criterionId);
            }
            if (result.review_results) {
                return result.review_results.find(c => c.criterion_id === criterionId);
            }

            return null;
        }

        // 从审查细则对象中提取结果文本
        function extractCriterionResult(criterion) {
            if (criterion.overall_result) {
                return criterion.overall_result + (criterion.comprehensive_analysis ? ': ' + criterion.comprehensive_analysis : '');
            }
            if (criterion.result) {
                return criterion.result + (criterion.analysis || criterion.explanation ? ': ' + (criterion.analysis || criterion.explanation) : '');
            }
            if (criterion.analysis_result) {
                return criterion.analysis_result;
            }

            return '无结果';
        }

        // 切换报告选择状态
        function toggleReportSelection(reportId) {
            if (selectedReports.has(reportId)) {
                selectedReports.delete(reportId);
            } else {
                selectedReports.add(reportId);
            }
            updateSelectedCount();
            displayReviews(allReviews); // 重新渲染以更新选择状态
        }

        // 更新选中数量显示
        function updateSelectedCount() {
            const countElement = document.getElementById('selectedCount');
            if (countElement) {
                countElement.textContent = `已选择 ${selectedReports.size} 个报告`;
            }
        }

        // 全选报告
        function selectAllReports() {
            // 获取当前显示的报告卡片对应的报告ID
            const currentReports = document.querySelectorAll('.review-card input[type="checkbox"]');
            currentReports.forEach(checkbox => {
                const reportId = checkbox.getAttribute('onchange').match(/'([^']+)'/)[1];
                selectedReports.add(reportId);
            });
            displayReports(allReports); // 重新渲染以更新选择状态
        }

        // 清空选择
        function clearAllReports() {
            selectedReports.clear();
            displayReports(allReports); // 重新渲染以更新选择状态
        }

        // 生成多报告汇总分析
        async function generateMultiReportSummary() {
            if (selectedReports.size === 0) {
                alert('请至少选择一个报告进行汇总分析');
                return;
            }

            if (!confirm(`确定要对选中的 ${selectedReports.size} 个报告进行汇总分析吗？`)) {
                return;
            }

            document.querySelector('.loading').style.display = 'block';

            try {
                const response = await fetch('/api/multi-report-summary', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        report_ids: Array.from(selectedReports)
                    })
                });
                const result = await response.json();

                if (result.success) {
                    alert('汇总分析完成！');
                    // 可以在这里添加显示汇总结果的逻辑
                    console.log('汇总分析结果:', result.data);

                    // 清空选择
                    clearAllReports();
                } else {
                    alert('汇总分析失败: ' + result.error);
                }
            } catch (error) {
                alert('汇总分析失败: ' + error.message);
            } finally {
                document.querySelector('.loading').style.display = 'none';
            }
        }

        // 批量评审
        async function startBatchReview() {
            if (selectedReports.size === 0) {
                alert('请至少选择一个报告进行评审');
                return;
            }

            if (!confirm(`确定要对选中的 ${selectedReports.size} 个报告进行批量评审吗？`)) {
                return;
            }

            document.querySelector('.loading').style.display = 'block';

            let successCount = 0;
            let failCount = 0;
            const totalCount = selectedReports.size;

            for (const reportId of selectedReports) {
                try {
                    const response = await fetch(`/api/reviews/${reportId}`, {
                        method: 'POST'
                    });
                    const result = await response.json();

                    if (result.success) {
                        successCount++;
                    } else {
                        failCount++;
                        console.error(`评审报告 ${reportId} 失败:`, result.error);
                    }
                } catch (error) {
                    failCount++;
                    console.error(`评审报告 ${reportId} 失败:`, error.message);
                }
            }

            document.querySelector('.loading').style.display = 'none';

            alert(`批量评审完成！\n成功: ${successCount} 个\n失败: ${failCount} 个\n总计: ${totalCount} 个`);

            // 清空选择并刷新列表
            clearAllReports();
            loadReviews();
            displayReports(allReports);
        }

        // 删除评审记录
        async function deleteReview(reviewId) {
            if (!confirm('确定要删除这条评审记录吗？此操作不可恢复。')) {
                return;
            }

            try {
                const response = await fetch(`/api/reviews/${reviewId}`, {
                    method: 'DELETE'
                });
                const result = await response.json();

                if (result.success) {
                    alert('评审记录删除成功');
                    loadReviews();
                    displayReports(allReports);
                } else {
                    alert('删除评审记录失败: ' + result.error);
                }
            } catch (error) {
                alert('删除评审记录失败: ' + error.message);
            }
        }
    </script>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
